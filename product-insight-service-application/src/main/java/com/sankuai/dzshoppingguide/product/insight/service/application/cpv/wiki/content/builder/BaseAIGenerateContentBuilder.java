package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.bo.AIGenerateContentBO;
import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.bo.AIGenerateContentVersionBO;
import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.service.AIGenerateContentCacheService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 16:50
 */
@Slf4j
public abstract class BaseAIGenerateContentBuilder<RESPONSE extends ContentQueryBaseResponse, REQUEST extends ContentQueryBaseRequest>
        implements AIGenerateContentBuilder<RESPONSE, REQUEST> {

    @Resource
    private AIGenerateContentCacheService contentCacheService;

    protected final Map<String, String> queryContent(AIGenerateBizTypeEnum bizType, Set<String> keys) throws Exception {
        Map<String, AIGenerateContentBO> contentBOMap = contentCacheService.batchGet(bizType, keys);
        return contentBOMap.values().stream()
                .filter(content -> Optional.ofNullable(content)
                        .map(AIGenerateContentBO::getContentVersion)
                        .map(AIGenerateContentVersionBO::getContent).isPresent())
                .collect(Collectors.toMap(
                        AIGenerateContentBO::getKey,
                        content -> content.getContentVersion().getContent(),
                        (v1, v2) -> v1
                ));
    }

    @Override
    public RESPONSE query(REQUEST request) {
        try {
            request.checkParams();
            return doQuery(request);
        } catch (Exception e) {
            log.error("{},request:{}", this.getClass().getSimpleName(), JSON.toJSONString(request), e);
            return handleQueryException(request, e);
        }
    }

    protected abstract RESPONSE doQuery(REQUEST request) throws Exception;

    protected abstract RESPONSE handleQueryException(REQUEST request, Exception e);

}
