package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.service;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.cpv.wiki.service.AIGenerateContentQueryService;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.AIGenerateContentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/6/24 10:11
 */
@Slf4j
@MdpPigeonServer(
        url = "com.sankuai.dzshoppingguide.product.insight.AIGenerateContentQueryService",
        useSharedPool = false,
        poolName = "AIGenerateContentQueryService"//使用专属线程池
)
@SuppressWarnings("rawtypes")
public class AIGenerateContentQueryServiceImpl implements AIGenerateContentQueryService, InitializingBean {

    @Resource
    private List<AIGenerateContentBuilder> builders;
    private final Map<AIGenerateBizTypeEnum, AIGenerateContentBuilder> builderMap = new HashMap<>();

    @SuppressWarnings("unchecked")
    @Override
    public ContentQueryBaseResponse batchQuery(ContentQueryBaseRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("request不能为null");
        }
        AIGenerateContentBuilder aiGenerateContentBuilder = builderMap.get(request.getBizType());
        if (aiGenerateContentBuilder == null) {
            throw new IllegalArgumentException("无效bizType:" + request.getBizType());
        }
        return aiGenerateContentBuilder.query(request);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (AIGenerateContentBuilder builder : builders) {
            builderMap.put(builder.getBizType(), builder);
        }
    }

}
