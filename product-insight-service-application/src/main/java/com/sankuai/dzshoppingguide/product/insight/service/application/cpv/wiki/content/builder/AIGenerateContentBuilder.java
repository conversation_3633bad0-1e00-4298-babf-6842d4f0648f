package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder;

import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 16:36
 */
public interface AIGenerateContentBuilder<RESPONSE extends ContentQueryBaseResponse, REQUEST extends ContentQueryBaseRequest> {

    RESPONSE query(REQUEST request);

    AIGenerateBizTypeEnum getBizType();

}
