package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.pet;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVWikiContentDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.BaseAIGenerateContentBuilder;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 16:55
 */
@Component
public class PetSingleValueWikiContentBuilder extends BaseAIGenerateContentBuilder
        <CPVSingleValueWikiQueryResponse, CPVSingleValueWikiQueryRequest> {

    @Override
    protected CPVSingleValueWikiQueryResponse doQuery(CPVSingleValueWikiQueryRequest request) throws Exception {
        Map<String, String> contentMap = doQueryContent(request.getBizType(), request.getKeys());
        Map<String, PetCPVWikiContentDTO> result = contentMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> JSON.parseObject(entry.getValue(), PetCPVWikiContentDTO.class)
                ));
        return CPVSingleValueWikiQueryResponse.builder().valueWikiMap(result).build();
    }

    /**
     * 内部会先做同义词匹配，然后用同义词查询，再转换为别名，对外不感知同义词匹配这个过程
     *
     * @param keys V的别名
     * @return Map<V的别名, Content>
     */
    private Map<String, String> doQueryContent(AIGenerateBizTypeEnum bizType, Set<String> keys) throws Exception {
        Map<String, String> aliasName2SameNameMap = keys.stream().collect(Collectors.toMap(
                key -> key,
                PetCPVSameNameUtils::getSameName
        ));
        Set<String> allSameNames = new HashSet<>(aliasName2SameNameMap.values());
        Map<String, String> sameName2ContentMap = super.queryContent(bizType, allSameNames);
        return aliasName2SameNameMap.entrySet().stream()
                .filter(entry -> !sameName2ContentMap.containsKey(entry.getValue()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> sameName2ContentMap.get(entry.getValue())
                ));
    }

    @Override
    protected CPVSingleValueWikiQueryResponse handleQueryException(CPVSingleValueWikiQueryRequest request, Exception e) {
        return new CPVSingleValueWikiQueryResponse().setFailure(e.getMessage());
    }

    @Override
    public AIGenerateBizTypeEnum getBizType() {
        return AIGenerateBizTypeEnum.PET_AI_WIKI_SINGLE_VALUE;
    }

}
