package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.pet;

import com.alibaba.fastjson.JSONObject;
import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVValueDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVWikiContentDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.BaseAIGenerateContentBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 16:57
 */
@Component
public class PetMultiValueWikiContentBuilder extends BaseAIGenerateContentBuilder
        <CPVMultiValueWikiQueryResponse, CPVMultiValueWikiQueryRequest> {

    @Override
    protected CPVMultiValueWikiQueryResponse doQuery(CPVMultiValueWikiQueryRequest request) throws Exception {
        //所有P值前面拼接_P，用以与V值区分
        final Set<String> formattedPropertyKeys = request.getCpvMap().keySet().stream().map(this::buildPropertyKey).collect(Collectors.toSet());
        //所有V值找同义词进行查询与单值逻辑一致
        final Map<String, String> valueAliasName2ValueSameNameMap = request.getCpvMap().values().stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(
                        valueAliasName -> valueAliasName,
                        PetCPVSameNameUtils::getSameName
                ));
        formattedPropertyKeys.addAll(valueAliasName2ValueSameNameMap.values());
        //获取所有AI生成的内容，但是key是formattedPropertyKeys和Value的SameName
        final Map<String, String> allContentMap = queryContent(this.getBizType(), formattedPropertyKeys);
        //转化成入参的propertyKey对应的内容
        final Map<String, String> propertyContentMap = request.getCpvMap().keySet().stream()
                .filter(propertyKey -> allContentMap.containsKey(buildPropertyKey(propertyKey)))
                .collect(Collectors.toMap(
                        propertyKey -> propertyKey,
                        propertyKey -> allContentMap.get(buildPropertyKey(propertyKey))
                ));
        //转化成入参Value值的aliasName对应的内容
        final Map<String, String> valueContentMap = request.getCpvMap().values().stream().flatMap(Collection::stream)
                .distinct()
                .filter(valueKey -> allContentMap.containsKey(valueAliasName2ValueSameNameMap.get(valueKey)))
                .collect(Collectors.toMap(
                        valueKey -> valueKey,
                        valueKey -> allContentMap.get(valueAliasName2ValueSameNameMap.get(valueKey))
                ));
        //构建返回值
        Map<String, PetCPVWikiContentDTO> result = request.getCpvMap().entrySet().stream()
                .map(entry -> buildMultiPetCPVWikiContentDTO(
                        entry.getKey(), entry.getValue(), propertyContentMap, valueContentMap
                ))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        Pair::getKey,
                        Pair::getValue
                ));
        return CPVMultiValueWikiQueryResponse.builder().valueWikiMap(result).build();
    }

    private Pair<String, PetCPVWikiContentDTO> buildMultiPetCPVWikiContentDTO(final String propertyName,
                                                                              final Set<String> valueNames,
                                                                              final Map<String, String> propertyContentMap,
                                                                              final Map<String, String> valueContentMap) {
        String propertyContentJSON = propertyContentMap.get(propertyName);
        if (StringUtils.isBlank(propertyContentJSON)) {
            return null;
        }
        PetCPVWikiContentDTO petCPVWikiContentDTO = JSONObject.parseObject(propertyContentJSON, PetCPVWikiContentDTO.class);
        if (petCPVWikiContentDTO == null) {
            return null;
        }
        List<PetCPVValueDTO> values = valueNames.stream().map(value -> {
            String valueContent = valueContentMap.get(value);
            if (StringUtils.isBlank(valueContent)) {
                return null;
            }
            return PetCPVValueDTO.builder().title(value).value(valueContent).build();
        }).filter(Objects::nonNull).toList();
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }
        petCPVWikiContentDTO.setValues(values);
        return new Pair<>(propertyName, petCPVWikiContentDTO);
    }

    private String buildPropertyKey(String propertyName) {
        return "P_" + propertyName;
    }

    @Override
    protected CPVMultiValueWikiQueryResponse handleQueryException(CPVMultiValueWikiQueryRequest request, Exception e) {
        return new CPVMultiValueWikiQueryResponse().setFailure(e.getMessage());
    }

    @Override
    public AIGenerateBizTypeEnum getBizType() {
        return AIGenerateBizTypeEnum.PET_AI_WIKI_MULTI_VALUE;
    }

}
