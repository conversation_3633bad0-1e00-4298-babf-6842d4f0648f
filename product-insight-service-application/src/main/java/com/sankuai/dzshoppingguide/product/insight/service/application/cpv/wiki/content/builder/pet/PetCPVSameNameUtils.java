package com.sankuai.dzshoppingguide.product.insight.service.application.cpv.wiki.content.builder.pet;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/6/26 14:31
 */
@Slf4j
@Component
public class PetCPVSameNameUtils implements InitializingBean {

    private final static String LION_KEY = "com.sankuai.dzshoppingguide.product.insight.pet.cpv.same.name";

    private static Map<String, String> sameNameMap;

    public static String getSameName(String name) {
        return sameNameMap.getOrDefault(name, name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        String json = Lion.getString(Environment.getAppName(), LION_KEY, null);
        initSameNameMap(json);
        Lion.addConfigListener(LION_KEY, configEvent -> initSameNameMap(configEvent.getValue()));
    }

    private void initSameNameMap(String json) {
        Map<String, Set<String>> sameNameConfig = JSONObject.parseObject(json, new TypeReference<>() {
        });
        if (MapUtils.isNotEmpty(sameNameConfig)) {
            return;
        }
        final Map<String, String> localSameNameMap = new HashMap<>();
        sameNameConfig.entrySet().stream()
                .map(entry -> entry.getValue().stream().collect(Collectors.toMap(
                        v -> v,
                        v -> entry.getKey()
                )))
                .forEach(localSameNameMap::putAll);
        PetCPVSameNameUtils.sameNameMap = localSameNameMap;
    }
}
