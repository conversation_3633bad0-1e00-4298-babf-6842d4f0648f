package com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value;

import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVWikiContentDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import lombok.*;

import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 15:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CPVMultiValueWikiQueryResponse extends ContentQueryBaseResponse {

    /**
     * key:CPV的P，入参里cpvMap的key
     */
    private Map<String, PetCPVWikiContentDTO> valueWikiMap;

    @Override
    public AIGenerateBizTypeEnum getBizType() {
        return AIGenerateBizTypeEnum.PET_AI_WIKI_MULTI_VALUE;
    }

}
