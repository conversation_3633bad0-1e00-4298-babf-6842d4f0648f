package com.sankuai.dzshoppingguide.product.insight.service.api.enums;

import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.multi.value.CPVMultiValueWikiQueryResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryRequest;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value.CPVSingleValueWikiQueryResponse;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/24 09:46
 */
@Getter
public enum AIGenerateBizTypeEnum {

    PET_AI_WIKI_SINGLE_VALUE(1, "宠物cpv单值解释",
            CPVSingleValueWikiQueryRequest.class,
            CPVSingleValueWikiQueryResponse.class),

    PET_AI_WIKI_MULTI_VALUE(2, "宠物cpv多值联合解释",
            CPVMultiValueWikiQueryRequest.class,
            CPVMultiValueWikiQueryResponse.class);

    private final int code;

    private final String desc;

    private final Class<? extends ContentQueryBaseRequest> queryRequestClass;

    private final Class<? extends ContentQueryBaseResponse> queryResponseClass;

    AIGenerateBizTypeEnum(int code, String desc, Class<? extends ContentQueryBaseRequest> queryRequestClass, Class<? extends ContentQueryBaseResponse> queryResponseClass) {
        this.code = code;
        this.desc = desc;
        this.queryRequestClass = queryRequestClass;
        this.queryResponseClass = queryResponseClass;
    }

    public static AIGenerateBizTypeEnum fromCode(int code) {
        for (AIGenerateBizTypeEnum value : AIGenerateBizTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (AIGenerateBizTypeEnum value : AIGenerateBizTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(AIGenerateBizTypeEnum.values()).collect(Collectors.toMap(
                AIGenerateBizTypeEnum::getCode,
                AIGenerateBizTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(AIGenerateBizTypeEnum.values()).collect(Collectors.toMap(
                AIGenerateBizTypeEnum::name,
                AIGenerateBizTypeEnum::getDesc
        ));
    }

}