package com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.single.value;

import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.ContentQueryBaseResponse;
import com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet.dto.PetCPVWikiContentDTO;
import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import lombok.*;

import java.util.Map;

/**
 * @Author: guangyu<PERSON>e
 * @Date: 2025/6/25 15:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CPVSingleValueWikiQueryResponse extends ContentQueryBaseResponse {

    /**
     * key:CPV的V
     */
    private Map<String, PetCPVWikiContentDTO> valueWikiMap;

    @Override
    public AIGenerateBizTypeEnum getBizType() {
        return AIGenerateBizTypeEnum.PET_AI_WIKI_SINGLE_VALUE;
    }

}
