package com.sankuai.dzshoppingguide.product.insight.service.api.content.struct.pet;

import com.sankuai.dzshoppingguide.product.insight.service.api.enums.AIGenerateBizTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/6/25 15:20
 */
@Data
public abstract class ContentQueryBaseResponse implements Serializable {

    private boolean success = true;

    private String msg;

    public abstract AIGenerateBizTypeEnum getBizType();

    @SuppressWarnings("unchecked")
    public <T extends ContentQueryBaseResponse> T setFailure(String errorMsg) {
        this.msg = errorMsg;
        return (T) this;
    }

}
