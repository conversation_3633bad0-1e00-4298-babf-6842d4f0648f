package com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.repository;

import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.bo.AIGenerateContentBO;
import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.bo.AIGenerateContentVersionBO;
import com.sankuai.dzshoppingguide.product.insight.service.domain.cpv.wiki.enums.AIGenerateBizTypeEnum;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.dao.AIGenerateContentPOExample;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.dao.AIGenerateContentPOMapper;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.dao.AIGenerateContentVersionPOExample;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.dao.AIGenerateContentVersionPOMapper;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.po.AIGenerateContentPO;
import com.sankuai.dzshoppingguide.product.insight.service.infrastructure.po.AIGenerateContentVersionPOWithBLOBs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/6/24 10:09
 */
@Slf4j
@Service
public class AIGenerateContentDBService {

    @Autowired
    private AIGenerateContentPOMapper contentPOMapper;

    @Autowired
    private AIGenerateContentVersionPOMapper versionPOMapper;

    /**
     * Save or update content and its version
     *
     * @param contentBO content to save
     * @return saved content with ID
     */
    @Transactional(rollbackFor = Exception.class)
    public long saveContent(AIGenerateContentBO contentBO) {
        try {
            // Set current time if not set
            if (contentBO.getAddTime() == null) {
                contentBO.setAddTime(new Date());
            }
            contentBO.setUpdateTime(new Date());

            // Convert BO to PO
            AIGenerateContentPO contentPO = convertContentToPO(contentBO);

            // Insert or update content
            if (contentPO.getId() == null || contentPO.getId() <= 0) {
                contentPOMapper.insertSelective(contentPO);
                contentBO.setContentId(contentPO.getId());
            } else {
                contentPOMapper.updateByPrimaryKeySelective(contentPO);
            }

            // Save content version if present
            if (contentBO.getContentVersion() != null) {
                saveContentVersion(contentBO);
            }

            return contentPO.getId();
        } catch (Exception e) {
            log.error("Failed to save content: {}", contentBO, e);
            throw new RuntimeException("Failed to save content", e);
        }
    }

    /**
     * Get content by ID
     *
     * @param contentId content ID
     * @return content or null if not found
     */
    public AIGenerateContentBO getContent(long contentId) {
        AIGenerateContentPO contentPO = contentPOMapper.selectByPrimaryKey(contentId);
        if (contentPO == null) {
            return null;
        }
        AIGenerateContentVersionPOExample example = new AIGenerateContentVersionPOExample();
        example.createCriteria()
                .andContentIdEqualTo(contentId)
                .andTemplateIdEqualTo(contentPO.getTemplateId());
        example.setOrderByClause("id DESC limit 1");
        List<AIGenerateContentVersionPOWithBLOBs> versions = versionPOMapper.selectByExample(example);
        if (versions.isEmpty()) {
            return null;
        }
        AIGenerateContentBO contentBO = convertContentToBO(contentPO);
        AIGenerateContentVersionBO contentVersionBO = convertVersionToBO(versions.get(0));
        contentBO.setContentVersion(contentVersionBO);
        return contentBO;
    }

    /**
     * Get contents by bizType and keys
     *
     * @param bizType business type
     * @param keys    set of keys to query
     * @return map of key to content, empty map if none found
     */
    public Map<String, AIGenerateContentBO> getContentsByBizTypeAndKeys(AIGenerateBizTypeEnum bizType,
                                                                        Set<String> keys) {
        if (bizType == null || keys == null || keys.isEmpty()) {
            return new HashMap<>();
        }
        // Create example for query
        AIGenerateContentPOExample contentExample = new AIGenerateContentPOExample();
        contentExample.createCriteria()
                .andBizTypeEqualTo(bizType.getCode())
                .andKeyIn(new ArrayList<>(keys));
        // Query contents
        List<AIGenerateContentPO> contentPOs = contentPOMapper.selectByExample(contentExample);
        if (contentPOs.isEmpty()) {
            return new HashMap<>();
        }
        //获取第一步查询结果所有的templateId，提高第二步查询version的效率
        List<Long> allContentIds = contentPOs.stream().map(AIGenerateContentPO::getId).toList();
        List<Long> allTemplateIds = contentPOs.stream().map(AIGenerateContentPO::getTemplateId).distinct().toList();
        AIGenerateContentVersionPOExample example = new AIGenerateContentVersionPOExample();
        example.createCriteria()
                .andContentIdIn(allContentIds)
                .andTemplateIdIn(allTemplateIds);
        List<AIGenerateContentVersionPOWithBLOBs> versionPOList = versionPOMapper.selectByExample(example);
        if (versionPOList.isEmpty()) {
            return new HashMap<>();
        }
        //组装结果
        Map<Long, List<AIGenerateContentVersionPOWithBLOBs>> versionPOGroupingByContentIdMap = versionPOList.stream()
                .collect(Collectors.groupingBy(AIGenerateContentVersionPOWithBLOBs::getContentId));
        return contentPOs.stream()
                .filter(contentPO -> versionPOGroupingByContentIdMap.containsKey(contentPO.getId()))
                .map(contentPO -> {
                    AIGenerateContentVersionPOWithBLOBs versionPO = versionPOGroupingByContentIdMap.get(contentPO.getId())
                            .stream()
                            .filter(vpo -> vpo.getTemplateId().equals(contentPO.getTemplateId()))
                            .findFirst()
                            .orElse(null);
                    if (versionPO == null) {
                        return null;
                    }
                    AIGenerateContentBO contentBO = convertContentToBO(contentPO);
                    AIGenerateContentVersionBO contentVersionBO = convertVersionToBO(versionPO);
                    contentBO.setContentVersion(contentVersionBO);
                    return contentBO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AIGenerateContentBO::getKey,
                        v -> v
                ));
    }


    /**
     * Delete content by ID
     *
     * @param contentId content ID
     * @return true if deleted, false if not found
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContentById(long contentId) {
        try {
            // Delete associated versions first
            AIGenerateContentVersionPOExample versionExample = new AIGenerateContentVersionPOExample();
            versionExample.createCriteria().andContentIdEqualTo(contentId);
            versionPOMapper.deleteByExample(versionExample);

            // Then delete the content
            return contentPOMapper.deleteByPrimaryKey(contentId) > 0;
        } catch (Exception e) {
            log.error("Failed to delete content with ID: {}", contentId, e);
            throw new RuntimeException("Failed to delete content", e);
        }
    }

    /**
     * Save content version
     *
     * @return saved version with ID
     */
    private long saveContentVersion(AIGenerateContentBO contentBO) {
        try {
            AIGenerateContentVersionBO versionBO = contentBO.getContentVersion();
            // Set current time if not set
            if (versionBO.getAddTime() == null) {
                versionBO.setAddTime(new Date());
            }

            // Convert BO to PO
            AIGenerateContentVersionPOWithBLOBs versionPO = convertVersionToPO(contentBO);

            // Insert or update version
            if (versionPO.getId() == null || versionPO.getId() <= 0) {
                versionPOMapper.insertSelective(versionPO);
                versionBO.setVersionId(versionPO.getId());
            } else {
                versionPOMapper.updateByPrimaryKeyWithBLOBs(versionPO);
            }

            return versionPO.getId();
        } catch (Exception e) {
            log.error("Failed to save content version: {}", contentBO, e);
            throw new RuntimeException("Failed to save content version", e);
        }
    }

    /**
     * Convert BO to PO
     */
    private AIGenerateContentPO convertContentToPO(AIGenerateContentBO bo) {
        AIGenerateContentPO po = new AIGenerateContentPO();
        po.setId(bo.getContentId() > 0 ? bo.getContentId() : null);
        po.setBizType(bo.getBizType() != null ? bo.getBizType().getCode() : null);
        po.setKey(bo.getKey());
        po.setTemplateId(bo.getTemplateId());
        po.setAddTime(bo.getAddTime());
        po.setUpdateTime(bo.getUpdateTime());
        return po;
    }

    /**
     * Convert PO to BO
     */
    private AIGenerateContentBO convertContentToBO(AIGenerateContentPO po) {
        AIGenerateContentBO bo = new AIGenerateContentBO();
        bo.setContentId(po.getId());
        bo.setBizType(po.getBizType() != null ? AIGenerateBizTypeEnum.fromCode(po.getBizType()) : null);
        bo.setKey(po.getKey());
        bo.setTemplateId(po.getTemplateId());
        bo.setAddTime(po.getAddTime());
        bo.setUpdateTime(po.getUpdateTime());
        return bo;
    }

    /**
     * Convert version BO to PO
     */
    private AIGenerateContentVersionPOWithBLOBs convertVersionToPO(AIGenerateContentBO contentBO) {
        AIGenerateContentVersionBO bo = contentBO.getContentVersion();
        AIGenerateContentVersionPOWithBLOBs po = new AIGenerateContentVersionPOWithBLOBs();
        po.setId(bo.getVersionId() > 0 ? bo.getVersionId() : null);
        po.setContentId(contentBO.getContentId());
        po.setTemplateId(contentBO.getTemplateId());
        po.setPrompt(bo.getPrompt());
        po.setContent(bo.getContent());
        po.setAddTime(bo.getAddTime());
        return po;
    }

    /**
     * Convert version PO to BO
     */
    private AIGenerateContentVersionBO convertVersionToBO(AIGenerateContentVersionPOWithBLOBs versionPO) {
        AIGenerateContentVersionBO bo = new AIGenerateContentVersionBO();
        bo.setVersionId(versionPO.getId());
        bo.setPrompt(versionPO.getPrompt());
        bo.setContent(versionPO.getContent());
        bo.setAddTime(versionPO.getAddTime());
        return bo;
    }

}
